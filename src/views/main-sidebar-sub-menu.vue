<template>
  <el-submenu
    v-if="menu.list && menu.list.length >= 1"
    :index="menu.menuId + ''"
    :popper-class="
      ('site-sidebar--' + sidebarLayoutSkin + '-popper', 'site-sidebar--scroll')
    "
  >
    <template slot="title">
      <icon-svg :name="getMenuIcon(menu)" class="site-sidebar__menu-icon" />
      <span>{{ menu.name }}</span>
    </template>
    <sub-menu
      v-for="item in menu.list"
      :key="item.menuId"
      :menu="item"
      :dynamicMenuRoutes="dynamicMenuRoutes"
    />
  </el-submenu>
  <el-menu-item v-else :index="menu.menuId + ''" @click="gotoRouteHandle(menu)">
    <icon-svg :name="getMenuIcon(menu)" class="site-sidebar__menu-icon" />
    <span>{{ menu.name }}</span>
  </el-menu-item>
</template>

<script>
import SubMenu from './main-sidebar-sub-menu'
export default {
  name: 'sub-menu',
  props: {
    menu: {
      type: Object,
      required: true,
    },
    dynamicMenuRoutes: {
      type: Array,
      required: true,
    },
  },
  components: {
    SubMenu,
  },
  computed: {
    sidebarLayoutSkin: {
      get() {
        return this.$store.state.common.sidebarLayoutSkin
      },
    },
  },
  methods: {
    // 获取菜单图标，如果为空则提供默认图标
    getMenuIcon(menu) {
      if (menu.icon && menu.icon.trim()) {
        return menu.icon
      }

      return 'bianji'
    },

    // 通过menuId与动态(菜单)路由进行匹配跳转至指定路由
    gotoRouteHandle(menu) {
      const route = this.dynamicMenuRoutes.filter(
        item => item.meta.menuId === menu.menuId
      )
      if (route.length >= 1) {
        this.$router.push({ name: route[0].name })
      }
    },
  },
}
</script>
<style></style>
