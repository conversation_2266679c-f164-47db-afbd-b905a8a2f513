<template>
  <el-submenu
    v-if="menu.list && menu.list.length >= 1"
    :index="menu.menuId + ''"
    :popper-class="
      ('site-sidebar--' + sidebarLayoutSkin + '-popper', 'site-sidebar--scroll')
    "
  >
    <template slot="title">
      <icon-svg :name="getMenuIcon(menu)" class="site-sidebar__menu-icon" />
      <span>{{ menu.name }}</span>
    </template>
    <sub-menu
      v-for="item in menu.list"
      :key="item.menuId"
      :menu="item"
      :dynamicMenuRoutes="dynamicMenuRoutes"
    />
  </el-submenu>
  <el-menu-item v-else :index="menu.menuId + ''" @click="gotoRouteHandle(menu)">
    <icon-svg :name="getMenuIcon(menu)" class="site-sidebar__menu-icon" />
    <span>{{ menu.name }}</span>
  </el-menu-item>
</template>

<script>
import SubMenu from './main-sidebar-sub-menu'
export default {
  name: 'sub-menu',
  props: {
    menu: {
      type: Object,
      required: true,
    },
    dynamicMenuRoutes: {
      type: Array,
      required: true,
    },
  },
  components: {
    SubMenu,
  },
  computed: {
    sidebarLayoutSkin: {
      get() {
        return this.$store.state.common.sidebarLayoutSkin
      },
    },
  },
  methods: {
    // 获取菜单图标，如果为空则提供默认图标
    getMenuIcon(menu) {
      if (menu.icon && menu.icon.trim()) {
        return menu.icon
      }

      // 根据菜单类型或名称提供默认图标
      if (menu.list && menu.list.length > 0) {
        // 有子菜单的父级菜单，根据名称智能选择图标
        const name = menu.name.toLowerCase()
        if (name.includes('系统') || name.includes('设置')) {
          return 'setting'
        } else if (name.includes('用户') || name.includes('人员')) {
          return 'user'
        } else if (name.includes('数据') || name.includes('统计')) {
          return 'chart'
        } else if (name.includes('内容') || name.includes('文章')) {
          return 'edit'
        } else {
          return 'folder'
        }
      } else {
        // 叶子菜单项，根据名称智能选择图标
        const name = menu.name.toLowerCase()
        if (name.includes('列表') || name.includes('管理')) {
          return 'list'
        } else if (name.includes('添加') || name.includes('新增')) {
          return 'plus'
        } else if (name.includes('设置') || name.includes('配置')) {
          return 'setting'
        } else if (name.includes('统计') || name.includes('报表')) {
          return 'chart'
        } else {
          return 'document'
        }
      }
    },

    // 通过menuId与动态(菜单)路由进行匹配跳转至指定路由
    gotoRouteHandle(menu) {
      const route = this.dynamicMenuRoutes.filter(
        item => item.meta.menuId === menu.menuId
      )
      if (route.length >= 1) {
        this.$router.push({ name: route[0].name })
      }
    },
  },
}
</script>
<style></style>
