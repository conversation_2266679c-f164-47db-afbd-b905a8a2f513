<template>
  <div class="dialog-debug-page">
    <div class="page-header">
      <h1>对话框闪烁诊断</h1>
      <p>专门用于诊断和测试对话框闪烁问题</p>
    </div>

    <div class="debug-info">
      <el-alert
        title="诊断说明"
        type="info"
        description="这个页面用于测试对话框是否还有闪烁问题。如果仍然闪烁，请检查浏览器控制台的错误信息。"
        show-icon
        :closable="false"
      />
    </div>

    <div class="test-buttons">
      <el-button type="primary" @click="openSimpleDialog">
        最简单对话框
      </el-button>
      
      <el-button type="success" @click="openNoAnimationDialog">
        无动画对话框
      </el-button>
      
      <el-button type="warning" @click="openDebugDialog">
        调试对话框
      </el-button>
    </div>

    <!-- 最简单的对话框 -->
    <el-dialog
      title="最简单对话框"
      :visible.sync="simpleDialogVisible"
      width="30%"
      :show-close="true"
      :close-on-click-modal="true"
    >
      <p>这是最简单的对话框，没有任何自定义样式。</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="simpleDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 无动画对话框 -->
    <el-dialog
      title="无动画对话框"
      :visible.sync="noAnimationDialogVisible"
      width="40%"
      :modal="true"
      :lock-scroll="true"
      class="no-animation-dialog"
    >
      <div>
        <p>这个对话框应该完全没有动画效果。</p>
        <p>如果还是闪烁，说明问题可能在Element UI本身或者浏览器渲染。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="noAnimationDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 调试对话框 -->
    <el-dialog
      title="调试对话框"
      :visible.sync="debugDialogVisible"
      width="50%"
      class="debug-dialog"
    >
      <div class="debug-content">
        <h3>调试信息</h3>
        <p><strong>浏览器：</strong>{{ browserInfo }}</p>
        <p><strong>用户代理：</strong>{{ userAgent }}</p>
        <p><strong>屏幕分辨率：</strong>{{ screenResolution }}</p>
        <p><strong>设备像素比：</strong>{{ devicePixelRatio }}</p>
        
        <el-divider />
        
        <h4>CSS 支持检测</h4>
        <p><strong>支持 transform3d：</strong>{{ supportsTransform3d ? '是' : '否' }}</p>
        <p><strong>支持 backface-visibility：</strong>{{ supportsBackfaceVisibility ? '是' : '否' }}</p>
        <p><strong>支持 will-change：</strong>{{ supportsWillChange ? '是' : '否' }}</p>
        
        <el-divider />
        
        <h4>建议</h4>
        <ul>
          <li v-if="!supportsTransform3d">您的浏览器不支持 transform3d，这可能导致动画问题</li>
          <li v-if="!supportsBackfaceVisibility">您的浏览器不支持 backface-visibility</li>
          <li v-if="devicePixelRatio > 2">高DPI屏幕可能会影响动画性能</li>
          <li v-if="isOldBrowser">建议升级浏览器以获得更好的性能</li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="debugDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DialogDebug',
  data() {
    return {
      simpleDialogVisible: false,
      noAnimationDialogVisible: false,
      debugDialogVisible: false,
      browserInfo: '',
      userAgent: '',
      screenResolution: '',
      devicePixelRatio: 1,
      supportsTransform3d: false,
      supportsBackfaceVisibility: false,
      supportsWillChange: false,
      isOldBrowser: false
    }
  },
  mounted() {
    this.detectBrowserInfo()
    this.detectCSSSupport()
  },
  methods: {
    openSimpleDialog() {
      console.log('打开简单对话框')
      this.simpleDialogVisible = true
    },
    
    openNoAnimationDialog() {
      console.log('打开无动画对话框')
      this.noAnimationDialogVisible = true
    },
    
    openDebugDialog() {
      console.log('打开调试对话框')
      this.debugDialogVisible = true
    },
    
    detectBrowserInfo() {
      this.userAgent = navigator.userAgent
      this.browserInfo = this.getBrowserName()
      this.screenResolution = `${screen.width}x${screen.height}`
      this.devicePixelRatio = window.devicePixelRatio || 1
      
      // 检测是否是老旧浏览器
      this.isOldBrowser = this.checkOldBrowser()
    },
    
    getBrowserName() {
      const ua = navigator.userAgent
      if (ua.includes('Chrome')) return 'Chrome'
      if (ua.includes('Firefox')) return 'Firefox'
      if (ua.includes('Safari')) return 'Safari'
      if (ua.includes('Edge')) return 'Edge'
      if (ua.includes('Opera')) return 'Opera'
      return '未知浏览器'
    },
    
    checkOldBrowser() {
      const ua = navigator.userAgent
      // 简单的老旧浏览器检测
      if (ua.includes('MSIE') || ua.includes('Trident')) return true
      if (ua.includes('Chrome/') && parseInt(ua.split('Chrome/')[1]) < 60) return true
      if (ua.includes('Firefox/') && parseInt(ua.split('Firefox/')[1]) < 55) return true
      return false
    },
    
    detectCSSSupport() {
      // 检测 CSS 特性支持
      const testElement = document.createElement('div')
      
      // 检测 transform3d
      testElement.style.transform = 'translateZ(0)'
      this.supportsTransform3d = testElement.style.transform !== ''
      
      // 检测 backface-visibility
      testElement.style.backfaceVisibility = 'hidden'
      this.supportsBackfaceVisibility = testElement.style.backfaceVisibility !== ''
      
      // 检测 will-change
      testElement.style.willChange = 'transform'
      this.supportsWillChange = testElement.style.willChange !== ''
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-debug-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    color: #2d3748;
    margin-bottom: 8px;
  }

  p {
    color: #64748b;
    font-size: 16px;
  }
}

.debug-info {
  margin-bottom: 24px;
}

.test-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
}

.debug-content {
  h3, h4 {
    color: #2d3748;
    margin-bottom: 12px;
  }
  
  p {
    margin-bottom: 8px;
    line-height: 1.6;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      color: #e53e3e;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>

<style lang="scss">
// 专门为调试对话框添加的样式
.no-animation-dialog {
  // 强制禁用所有动画
  .el-dialog,
  .el-dialog__wrapper {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
}

.debug-dialog {
  // 保持默认样式，用于对比
}
</style>
